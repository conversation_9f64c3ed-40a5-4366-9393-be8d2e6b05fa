# SQL Server Connector Image Requirements

We need a Docker image based on Ubuntu 22.04 with the following components installed:

1. SQL Server Connector (mssql-tools18)
2. Azure CLI (az)
3. ODBC Development Files

## Installation Commands

The following commands should be included in the Dockerfile or setup script:

```bash
# Use Ubuntu 22.04 as base image
FROM ubuntu:22.04

# Add Microsoft repository keys and sources
curl https://packages.microsoft.com/keys/microsoft.asc | sudo tee /etc/apt/trusted.gpg.d/microsoft.asc
curl https://packages.microsoft.com/config/ubuntu/22.04/prod.list | sudo tee /etc/apt/sources.list.d/mssql-release.list

# Update package list and install required packages
sudo apt-get update
sudo apt-get install -y mssql-tools18 unixodbc-dev
```

This setup will allow for:
- Easy database connection testing
- Azure cloud integration
- ODBC connectivity
